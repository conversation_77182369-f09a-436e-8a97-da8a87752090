Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) for USB_LP_CAN1_RX0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to can_driver.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    main.o(i.main) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    main.o(i.main) refers to can_app.o(i.CAN_BOOT_ProgramDatatoFlash) for CAN_BOOT_ProgramDatatoFlash
    main.o(i.main) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to can_driver.o(i.CAN_Configuration) for CAN_Configuration
    main.o(i.main) refers to can_app.o(i.CAN_BOOT_ExecutiveCommand) for CAN_BOOT_ExecutiveCommand
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to can_driver.o(.data) for CAN1_CanRxMsgFlag
    main.o(i.main) refers to can_driver.o(.bss) for CAN1_RxMessage
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    can_driver.o(i.BOOT_DelayMs) refers to can_driver.o(.data) for .data
    can_driver.o(i.BOOT_TIM_Config) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    can_driver.o(i.BOOT_TIM_Config) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can_driver.o(i.BOOT_TIM_Config) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    can_driver.o(i.BOOT_TIM_Config) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    can_driver.o(i.BOOT_TIM_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    can_driver.o(i.BOOT_TIM_Config) refers to can_driver.o(.data) for .data
    can_driver.o(i.CAN_ConfigFilter) refers to stm32f10x_can.o(i.CAN_FilterInit) for CAN_FilterInit
    can_driver.o(i.CAN_Configuration) refers to can_driver.o(i.CAN_NVIC_Configuration) for CAN_NVIC_Configuration
    can_driver.o(i.CAN_Configuration) refers to can_driver.o(i.CAN_GPIO_Configuration) for CAN_GPIO_Configuration
    can_driver.o(i.CAN_Configuration) refers to stm32f10x_can.o(i.CAN_StructInit) for CAN_StructInit
    can_driver.o(i.CAN_Configuration) refers to can_driver.o(i.CAN_GetBaudRateNum) for CAN_GetBaudRateNum
    can_driver.o(i.CAN_Configuration) refers to stm32f10x_can.o(i.CAN_Init) for CAN_Init
    can_driver.o(i.CAN_Configuration) refers to can_driver.o(i.CAN_ConfigFilter) for CAN_ConfigFilter
    can_driver.o(i.CAN_Configuration) refers to stm32f10x_can.o(i.CAN_ITConfig) for CAN_ITConfig
    can_driver.o(i.CAN_Configuration) refers to can_driver.o(.data) for .data
    can_driver.o(i.CAN_GPIO_Configuration) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    can_driver.o(i.CAN_GPIO_Configuration) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can_driver.o(i.CAN_GPIO_Configuration) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    can_driver.o(i.CAN_NVIC_Configuration) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    can_driver.o(i.CAN_NVIC_Configuration) refers to misc.o(i.NVIC_Init) for NVIC_Init
    can_driver.o(i.CAN_WriteData) refers to stm32f10x_can.o(i.CAN_Transmit) for CAN_Transmit
    can_driver.o(i.CAN_WriteData) refers to delay.o(i.delay_ms) for delay_ms
    can_driver.o(i.CAN_WriteData) refers to stm32f10x_can.o(i.CAN_TransmitStatus) for CAN_TransmitStatus
    can_driver.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    can_driver.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    can_driver.o(i.TIM2_IRQHandler) refers to can_driver.o(.data) for .data
    can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f10x_can.o(i.CAN_Receive) for CAN_Receive
    can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f10x_can.o(i.CAN_ClearITPendingBit) for CAN_ClearITPendingBit
    can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to can_app.o(i.GetNAD) for GetNAD
    can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to can_driver.o(.bss) for .bss
    can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to can_driver.o(.data) for .data
    can_app.o(i.CAN_BOOT_ErasePage) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    can_app.o(i.CAN_BOOT_ErasePage) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    can_app.o(i.CAN_BOOT_ErasePage) refers to stm32f10x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    can_app.o(i.CAN_BOOT_ErasePage) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    can_app.o(i.CAN_BOOT_ExecutiveCommand) refers to can_app.o(i.GetNAD) for GetNAD
    can_app.o(i.CAN_BOOT_ExecutiveCommand) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    can_app.o(i.CAN_BOOT_ExecutiveCommand) refers to can_app.o(i.CAN_BOOT_ErasePage) for CAN_BOOT_ErasePage
    can_app.o(i.CAN_BOOT_ExecutiveCommand) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    can_app.o(i.CAN_BOOT_ExecutiveCommand) refers to can_driver.o(i.CAN_WriteData) for CAN_WriteData
    can_app.o(i.CAN_BOOT_JumpToApplication) refers to core_cm3.o(.emb_text) for __set_MSP
    can_app.o(i.CAN_BOOT_ProgramDatatoFlash) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    can_app.o(i.CAN_BOOT_ProgramDatatoFlash) refers to stm32f10x_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    can_app.o(i.crc16_ccitt) refers to can_app.o(.constdata) for .constdata


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f10x_md.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (100 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (68 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (72 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (36 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (140 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (90 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (28 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (228 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (36 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (44 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (200 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (98 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (102 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (44 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (36 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (144 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (152 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (22 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (84 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (42 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (108 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (300 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (156 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (62 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (48 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (172 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (28 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing can_driver.o(i.BOOT_DelayMs), (40 bytes).
    Removing can_driver.o(i.BOOT_TIM_Config), (112 bytes).
    Removing can_app.o(i.CAN_BOOT_JumpToApplication), (56 bytes).
    Removing can_app.o(.constdata), (512 bytes).
    Removing can_app.o(i.crc16_ccitt), (44 bytes).

457 unused section(s) (total 14756 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ..\Libraries\CMSIS\CM3\CoreSupport\core_cm3.c 0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\Libraries\CMSIS\CM3\DeviceSupport\ST\STM32F10x\startup\arm\startup_stm32f10x_md.s 0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c 0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c 0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c 0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_cec.c 0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_crc.c 0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c 0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c 0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c 0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c 0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c 0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_sdio.c 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_spi.c 0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\User\can_app.c                        0x00000000   Number         0  can_app.o ABSOLUTE
    ..\User\can_driver.c                     0x00000000   Number         0  can_driver.o ABSOLUTE
    ..\User\delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f10x_it.c                   0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\User\system_stm32f10x.c               0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\\Libraries\\CMSIS\\CM3\\CoreSupport\\core_cm3.c 0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08008000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080080ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080080ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080080f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080080f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080080f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080080f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080080fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080080fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080080fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080080fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08008100   Section       36  startup_stm32f10x_md.o(.text)
    .text                                    0x08008124   Section       36  init.o(.text)
    i.BusFault_Handler                       0x08008148   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.CAN_BOOT_ErasePage                     0x0800814a   Section        0  can_app.o(i.CAN_BOOT_ErasePage)
    i.CAN_BOOT_ExecutiveCommand              0x0800817c   Section        0  can_app.o(i.CAN_BOOT_ExecutiveCommand)
    i.CAN_BOOT_ProgramDatatoFlash            0x0800826c   Section        0  can_app.o(i.CAN_BOOT_ProgramDatatoFlash)
    i.CAN_ClearITPendingBit                  0x080082b0   Section        0  stm32f10x_can.o(i.CAN_ClearITPendingBit)
    i.CAN_ConfigFilter                       0x08008328   Section        0  can_driver.o(i.CAN_ConfigFilter)
    i.CAN_Configuration                      0x0800837c   Section        0  can_driver.o(i.CAN_Configuration)
    i.CAN_FilterInit                         0x08008428   Section        0  stm32f10x_can.o(i.CAN_FilterInit)
    i.CAN_GPIO_Configuration                 0x080084f4   Section        0  can_driver.o(i.CAN_GPIO_Configuration)
    i.CAN_GetBaudRateNum                     0x08008544   Section        0  can_driver.o(i.CAN_GetBaudRateNum)
    i.CAN_ITConfig                           0x080086c4   Section        0  stm32f10x_can.o(i.CAN_ITConfig)
    i.CAN_Init                               0x080086d4   Section        0  stm32f10x_can.o(i.CAN_Init)
    i.CAN_NVIC_Configuration                 0x080087bc   Section        0  can_driver.o(i.CAN_NVIC_Configuration)
    i.CAN_Receive                            0x080087e4   Section        0  stm32f10x_can.o(i.CAN_Receive)
    i.CAN_StructInit                         0x08008874   Section        0  stm32f10x_can.o(i.CAN_StructInit)
    i.CAN_Transmit                           0x08008894   Section        0  stm32f10x_can.o(i.CAN_Transmit)
    i.CAN_TransmitStatus                     0x08008938   Section        0  stm32f10x_can.o(i.CAN_TransmitStatus)
    i.CAN_WriteData                          0x080089a4   Section        0  can_driver.o(i.CAN_WriteData)
    i.DebugMon_Handler                       0x080089d8   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.FLASH_ClearFlag                        0x080089dc   Section        0  stm32f10x_flash.o(i.FLASH_ClearFlag)
    i.FLASH_ErasePage                        0x080089e8   Section        0  stm32f10x_flash.o(i.FLASH_ErasePage)
    i.FLASH_GetBank1Status                   0x08008a24   Section        0  stm32f10x_flash.o(i.FLASH_GetBank1Status)
    i.FLASH_Lock                             0x08008a4c   Section        0  stm32f10x_flash.o(i.FLASH_Lock)
    i.FLASH_ProgramWord                      0x08008a5c   Section        0  stm32f10x_flash.o(i.FLASH_ProgramWord)
    i.FLASH_Unlock                           0x08008aac   Section        0  stm32f10x_flash.o(i.FLASH_Unlock)
    i.FLASH_WaitForLastOperation             0x08008ac4   Section        0  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    i.GPIO_Init                              0x08008ae8   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GetNAD                                 0x08008b8c   Section        0  can_app.o(i.GetNAD)
    i.HardFault_Handler                      0x08008bb8   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08008bba   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08008bbc   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08008bc0   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08008c24   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08008c38   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08008c3c   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08008c54   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.SVC_Handler                            0x08008c6c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClockTo72                        0x08008c70   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08008c71   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x08008d18   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08008d30   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08008d34   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08008d88   Section        0  can_driver.o(i.TIM2_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08008db0   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_GetITStatus                        0x08008db6   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.USB_LP_CAN1_RX0_IRQHandler             0x08008dd0   Section        0  can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler)
    i.UsageFault_Handler                     0x08008e1c   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08008e1e   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08008e2c   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08008e2e   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.delay_init                             0x08008e3c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08008e78   Section        0  delay.o(i.delay_ms)
    i.main                                   0x08008eac   Section        0  main.o(i.main)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section        4  main.o(.data)
    data                                     0x20000014   Data           4  main.o(.data)
    .data                                    0x20000018   Section        4  delay.o(.data)
    fac_us                                   0x20000018   Data           1  delay.o(.data)
    fac_ms                                   0x2000001a   Data           2  delay.o(.data)
    .data                                    0x2000001c   Section      152  can_driver.o(.data)
    .bss                                     0x200000b4   Section       20  can_driver.o(.bss)
    STACK                                    0x200000c8   Section     1024  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08008000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080080ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080080ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080080ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080080f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080080f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080080f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080080f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080080f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080080fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080080fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08008101   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800811b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __scatterload                            0x08008125   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08008125   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08008149   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    CAN_BOOT_ErasePage                       0x0800814b   Thumb Code    48  can_app.o(i.CAN_BOOT_ErasePage)
    CAN_BOOT_ExecutiveCommand                0x0800817d   Thumb Code   228  can_app.o(i.CAN_BOOT_ExecutiveCommand)
    CAN_BOOT_ProgramDatatoFlash              0x0800826d   Thumb Code    64  can_app.o(i.CAN_BOOT_ProgramDatatoFlash)
    CAN_ClearITPendingBit                    0x080082b1   Thumb Code   116  stm32f10x_can.o(i.CAN_ClearITPendingBit)
    CAN_ConfigFilter                         0x08008329   Thumb Code    82  can_driver.o(i.CAN_ConfigFilter)
    CAN_Configuration                        0x0800837d   Thumb Code   162  can_driver.o(i.CAN_Configuration)
    CAN_FilterInit                           0x08008429   Thumb Code   194  stm32f10x_can.o(i.CAN_FilterInit)
    CAN_GPIO_Configuration                   0x080084f5   Thumb Code    74  can_driver.o(i.CAN_GPIO_Configuration)
    CAN_GetBaudRateNum                       0x08008545   Thumb Code   356  can_driver.o(i.CAN_GetBaudRateNum)
    CAN_ITConfig                             0x080086c5   Thumb Code    16  stm32f10x_can.o(i.CAN_ITConfig)
    CAN_Init                                 0x080086d5   Thumb Code   232  stm32f10x_can.o(i.CAN_Init)
    CAN_NVIC_Configuration                   0x080087bd   Thumb Code    40  can_driver.o(i.CAN_NVIC_Configuration)
    CAN_Receive                              0x080087e5   Thumb Code   144  stm32f10x_can.o(i.CAN_Receive)
    CAN_StructInit                           0x08008875   Thumb Code    32  stm32f10x_can.o(i.CAN_StructInit)
    CAN_Transmit                             0x08008895   Thumb Code   164  stm32f10x_can.o(i.CAN_Transmit)
    CAN_TransmitStatus                       0x08008939   Thumb Code    88  stm32f10x_can.o(i.CAN_TransmitStatus)
    CAN_WriteData                            0x080089a5   Thumb Code    48  can_driver.o(i.CAN_WriteData)
    DebugMon_Handler                         0x080089d9   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    FLASH_ClearFlag                          0x080089dd   Thumb Code     6  stm32f10x_flash.o(i.FLASH_ClearFlag)
    FLASH_ErasePage                          0x080089e9   Thumb Code    56  stm32f10x_flash.o(i.FLASH_ErasePage)
    FLASH_GetBank1Status                     0x08008a25   Thumb Code    34  stm32f10x_flash.o(i.FLASH_GetBank1Status)
    FLASH_Lock                               0x08008a4d   Thumb Code    12  stm32f10x_flash.o(i.FLASH_Lock)
    FLASH_ProgramWord                        0x08008a5d   Thumb Code    76  stm32f10x_flash.o(i.FLASH_ProgramWord)
    FLASH_Unlock                             0x08008aad   Thumb Code    12  stm32f10x_flash.o(i.FLASH_Unlock)
    FLASH_WaitForLastOperation               0x08008ac5   Thumb Code    36  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    GPIO_Init                                0x08008ae9   Thumb Code   162  stm32f10x_gpio.o(i.GPIO_Init)
    GetNAD                                   0x08008b8d   Thumb Code    38  can_app.o(i.GetNAD)
    HardFault_Handler                        0x08008bb9   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08008bbb   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08008bbd   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08008bc1   Thumb Code    94  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08008c25   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08008c39   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08008c3d   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08008c55   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    SVC_Handler                              0x08008c6d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08008d19   Thumb Code    24  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08008d31   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08008d35   Thumb Code    62  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08008d89   Thumb Code    34  can_driver.o(i.TIM2_IRQHandler)
    TIM_ClearITPendingBit                    0x08008db1   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_GetITStatus                          0x08008db7   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    USB_LP_CAN1_RX0_IRQHandler               0x08008dd1   Thumb Code    64  can_driver.o(i.USB_LP_CAN1_RX0_IRQHandler)
    UsageFault_Handler                       0x08008e1d   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08008e1f   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08008e2d   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08008e2f   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    delay_init                               0x08008e3d   Thumb Code    46  delay.o(i.delay_init)
    delay_ms                                 0x08008e79   Thumb Code    46  delay.o(i.delay_ms)
    main                                     0x08008ead   Thumb Code    62  main.o(i.main)
    Region$$Table$$Base                      0x08008f00   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008f20   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    CAN1_CanRxMsgFlag                        0x2000001c   Data           1  can_driver.o(.data)
    TimeOutFlag                              0x2000001d   Data           1  can_driver.o(.data)
    CAN_BaudRateInitTab                      0x2000001e   Data         150  can_driver.o(.data)
    CAN1_RxMessage                           0x200000b4   Data          20  can_driver.o(.bss)
    __initial_sp                             0x200004c8   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080080ed

  Load Region LR_IROM1 (Base: 0x08008000, Size: 0x00000fd4, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08008000, Load base: 0x08008000, Size: 0x00000f20, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08008000   0x08008000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080080ec   0x080080ec   0x00000000   Code   RO         3332  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080080ec   0x080080ec   0x00000004   Code   RO         3335    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080080f0   0x080080f0   0x00000004   Code   RO         3338    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080080f4   0x080080f4   0x00000000   Code   RO         3340    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080080f4   0x080080f4   0x00000000   Code   RO         3342    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080080f4   0x080080f4   0x00000008   Code   RO         3343    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080080fc   0x080080fc   0x00000000   Code   RO         3345    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080080fc   0x080080fc   0x00000000   Code   RO         3347    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080080fc   0x080080fc   0x00000004   Code   RO         3336    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08008100   0x08008100   0x00000024   Code   RO            4    .text               startup_stm32f10x_md.o
    0x08008124   0x08008124   0x00000024   Code   RO         3349    .text               mc_w.l(init.o)
    0x08008148   0x08008148   0x00000002   Code   RO         3122    i.BusFault_Handler  stm32f10x_it.o
    0x0800814a   0x0800814a   0x00000030   Code   RO         3281    i.CAN_BOOT_ErasePage  can_app.o
    0x0800817a   0x0800817a   0x00000002   PAD
    0x0800817c   0x0800817c   0x000000f0   Code   RO         3282    i.CAN_BOOT_ExecutiveCommand  can_app.o
    0x0800826c   0x0800826c   0x00000044   Code   RO         3284    i.CAN_BOOT_ProgramDatatoFlash  can_app.o
    0x080082b0   0x080082b0   0x00000078   Code   RO          477    i.CAN_ClearITPendingBit  stm32f10x_can.o
    0x08008328   0x08008328   0x00000052   Code   RO         3213    i.CAN_ConfigFilter  can_driver.o
    0x0800837a   0x0800837a   0x00000002   PAD
    0x0800837c   0x0800837c   0x000000ac   Code   RO         3214    i.CAN_Configuration  can_driver.o
    0x08008428   0x08008428   0x000000cc   Code   RO          481    i.CAN_FilterInit    stm32f10x_can.o
    0x080084f4   0x080084f4   0x00000050   Code   RO         3215    i.CAN_GPIO_Configuration  can_driver.o
    0x08008544   0x08008544   0x00000180   Code   RO         3216    i.CAN_GetBaudRateNum  can_driver.o
    0x080086c4   0x080086c4   0x00000010   Code   RO          487    i.CAN_ITConfig      stm32f10x_can.o
    0x080086d4   0x080086d4   0x000000e8   Code   RO          488    i.CAN_Init          stm32f10x_can.o
    0x080087bc   0x080087bc   0x00000028   Code   RO         3217    i.CAN_NVIC_Configuration  can_driver.o
    0x080087e4   0x080087e4   0x00000090   Code   RO          491    i.CAN_Receive       stm32f10x_can.o
    0x08008874   0x08008874   0x00000020   Code   RO          494    i.CAN_StructInit    stm32f10x_can.o
    0x08008894   0x08008894   0x000000a4   Code   RO          496    i.CAN_Transmit      stm32f10x_can.o
    0x08008938   0x08008938   0x0000006c   Code   RO          497    i.CAN_TransmitStatus  stm32f10x_can.o
    0x080089a4   0x080089a4   0x00000034   Code   RO         3218    i.CAN_WriteData     can_driver.o
    0x080089d8   0x080089d8   0x00000002   Code   RO         3123    i.DebugMon_Handler  stm32f10x_it.o
    0x080089da   0x080089da   0x00000002   PAD
    0x080089dc   0x080089dc   0x0000000c   Code   RO          990    i.FLASH_ClearFlag   stm32f10x_flash.o
    0x080089e8   0x080089e8   0x0000003c   Code   RO          995    i.FLASH_ErasePage   stm32f10x_flash.o
    0x08008a24   0x08008a24   0x00000028   Code   RO          996    i.FLASH_GetBank1Status  stm32f10x_flash.o
    0x08008a4c   0x08008a4c   0x00000010   Code   RO         1005    i.FLASH_Lock        stm32f10x_flash.o
    0x08008a5c   0x08008a5c   0x00000050   Code   RO         1010    i.FLASH_ProgramWord  stm32f10x_flash.o
    0x08008aac   0x08008aac   0x00000018   Code   RO         1013    i.FLASH_Unlock      stm32f10x_flash.o
    0x08008ac4   0x08008ac4   0x00000024   Code   RO         1017    i.FLASH_WaitForLastOperation  stm32f10x_flash.o
    0x08008ae8   0x08008ae8   0x000000a2   Code   RO         1288    i.GPIO_Init         stm32f10x_gpio.o
    0x08008b8a   0x08008b8a   0x00000002   PAD
    0x08008b8c   0x08008b8c   0x0000002c   Code   RO         3285    i.GetNAD            can_app.o
    0x08008bb8   0x08008bb8   0x00000002   Code   RO         3124    i.HardFault_Handler  stm32f10x_it.o
    0x08008bba   0x08008bba   0x00000002   Code   RO         3125    i.MemManage_Handler  stm32f10x_it.o
    0x08008bbc   0x08008bbc   0x00000002   Code   RO         3126    i.NMI_Handler       stm32f10x_it.o
    0x08008bbe   0x08008bbe   0x00000002   PAD
    0x08008bc0   0x08008bc0   0x00000064   Code   RO          132    i.NVIC_Init         misc.o
    0x08008c24   0x08008c24   0x00000014   Code   RO          133    i.NVIC_PriorityGroupConfig  misc.o
    0x08008c38   0x08008c38   0x00000002   Code   RO         3127    i.PendSV_Handler    stm32f10x_it.o
    0x08008c3a   0x08008c3a   0x00000002   PAD
    0x08008c3c   0x08008c3c   0x00000018   Code   RO         1704    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08008c54   0x08008c54   0x00000018   Code   RO         1706    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08008c6c   0x08008c6c   0x00000002   Code   RO         3128    i.SVC_Handler       stm32f10x_it.o
    0x08008c6e   0x08008c6e   0x00000002   PAD
    0x08008c70   0x08008c70   0x000000a8   Code   RO           10    i.SetSysClockTo72   system_stm32f10x.o
    0x08008d18   0x08008d18   0x00000018   Code   RO          136    i.SysTick_CLKSourceConfig  misc.o
    0x08008d30   0x08008d30   0x00000002   Code   RO         3129    i.SysTick_Handler   stm32f10x_it.o
    0x08008d32   0x08008d32   0x00000002   PAD
    0x08008d34   0x08008d34   0x00000054   Code   RO           12    i.SystemInit        system_stm32f10x.o
    0x08008d88   0x08008d88   0x00000028   Code   RO         3219    i.TIM2_IRQHandler   can_driver.o
    0x08008db0   0x08008db0   0x00000006   Code   RO         2330    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08008db6   0x08008db6   0x00000018   Code   RO         2356    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08008dce   0x08008dce   0x00000002   PAD
    0x08008dd0   0x08008dd0   0x0000004c   Code   RO         3220    i.USB_LP_CAN1_RX0_IRQHandler  can_driver.o
    0x08008e1c   0x08008e1c   0x00000002   Code   RO         3130    i.UsageFault_Handler  stm32f10x_it.o
    0x08008e1e   0x08008e1e   0x0000000e   Code   RO         3353    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08008e2c   0x08008e2c   0x00000002   Code   RO         3354    i.__scatterload_null  mc_w.l(handlers.o)
    0x08008e2e   0x08008e2e   0x0000000e   Code   RO         3355    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08008e3c   0x08008e3c   0x0000003c   Code   RO         3185    i.delay_init        delay.o
    0x08008e78   0x08008e78   0x00000034   Code   RO         3186    i.delay_ms          delay.o
    0x08008eac   0x08008eac   0x00000054   Code   RO         3088    i.main              main.o
    0x08008f00   0x08008f00   0x00000020   Data   RO         3351    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08008f20, Size: 0x000004c8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08008f20   0x00000014   Data   RW           13    .data               system_stm32f10x.o
    0x20000014   0x08008f34   0x00000004   Data   RW         3089    .data               main.o
    0x20000018   0x08008f38   0x00000004   Data   RW         3188    .data               delay.o
    0x2000001c   0x08008f3c   0x00000098   Data   RW         3222    .data               can_driver.o
    0x200000b4        -       0x00000014   Zero   RW         3221    .bss                can_driver.o
    0x200000c8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       400         22          0          0          0      18771   can_app.o
       926         66          0        152         20       6228   can_driver.o
       112         20          0          4          0       1252   delay.o
        84         22          0          4          0      15810   main.o
       144         16          0          0          0       4177   misc.o
        36          8        236          0       1024        968   startup_stm32f10x_md.o
      1020         34          0          0          0       7366   stm32f10x_can.o
       268         36          0          0          0       6469   stm32f10x_flash.o
       162          0          0          0          0       2036   stm32f10x_gpio.o
        18          0          0          0          0       4134   stm32f10x_it.o
        48         12          0          0          0       1330   stm32f10x_rcc.o
        30          0          0          0          0       2853   stm32f10x_tim.o
       252         30          0         20          0     253885   system_stm32f10x.o

    ----------------------------------------------------------------------
      3518        <USER>        <GROUP>        180       1044     325279   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o

    ----------------------------------------------------------------------
        86         <USER>          <GROUP>          0          0         68   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        86         16          0          0          0         68   mc_w.l

    ----------------------------------------------------------------------
        86         <USER>          <GROUP>          0          0         68   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3604        282        268        180       1044     323211   Grand Totals
      3604        282        268        180       1044     323211   ELF Image Totals
      3604        282        268        180          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3872 (   3.78kB)
    Total RW  Size (RW Data + ZI Data)              1224 (   1.20kB)
    Total ROM Size (Code + RO Data + RW Data)       4052 (   3.96kB)

==============================================================================

