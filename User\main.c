/**
  ******************************************************************************
  * @file    main.c
  * $Author: zx
  * $Revision: v10
  * $Date:: 2024-02-06 
  * @brief   主函数.
  ******************************************************************************
  * @attention
  *
  * 
  ******************************************************************************
  */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "can_driver.h"
#include "can_app.h"
#include "delay.h"
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
extern CanRxMsg CAN1_RxMessage; //定义can通信初始化的参数
extern volatile uint8_t CAN1_CanRxMsgFlag;//接收到CAN数据后的标志，没有接收时0，接收后是1
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
  * @brief  主函数，实现LED灯的闪烁
  */
int main(void)
{
  //首先初始化芯片配置，让芯片的时钟配置好，芯片可以运行
  
	//如果第一次启动指定地址没有数据，则写入标志数据。
  if(*((uint32_t *)APP_EXE_FLAG_ADDR)==0xFFFFFFFF){
    __align(4) static unsigned char data[4]={0x12,0x34,0x56,0x78};//自己定义的标志数据
    FLASH_Unlock();//芯片的flash解锁
    CAN_BOOT_ProgramDatatoFlash(APP_EXE_FLAG_ADDR,data,4);//将标志数据写入到指定的地址
    FLASH_Lock();  //芯片flash上锁
  }
  __set_PRIMASK(0);//开启总中断
	delay_init();    //延时初始化
	//配置can的A11-A12引脚为can通信
	//设置通信速度是1M
	//设置can通信只接收id地址为0x3C发来的数据，可自行修改
  CAN_Configuration(1000000);
  //设置读保护,保护程序破解，起到保密的作用。也可不启动
// 	if(FLASH_OB_GetRDP() != SET)
// 	{
// 		FLASH_OB_Unlock();
// 		FLASH_OB_RDPConfig(OB_RDP_Level_1); 
// 		FLASH_OB_Launch();
// 		FLASH_OB_Lock();		
// 	}
  while (1)
  {
		 //CAN收到了数据，进行数据处理
    if(CAN1_CanRxMsgFlag){
      CAN1_CanRxMsgFlag = 0;
      CAN_BOOT_ExecutiveCommand(&CAN1_RxMessage);//对数据进行解析处理
    }
  }
}

#ifdef  USE_FULL_ASSERT

/**
  * @brief  Reports the name of the source file and the source line number
  *   where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{ 
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1)
  {
  }
}
#endif

/***********************************文件结束***********************************/
