{"name": "Project", "target": "Project", "toolchain": "AC5", "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.14\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 16, "rootDir": "c:\\Users\\<USER>\\Desktop\\基于STM32F103的CAN bootloader程序源码资料\\CAN bootloader\\app\\RVMDK", "dumpPath": "build\\Project", "outDir": "build\\Project", "ram": 20480, "rom": 131072, "incDirs": ["../Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x", "../Libraries/STM32F10x_StdPeriph_Driver/inc", "../Libraries/CMSIS/CM3/CoreSupport", "../User"], "libDirs": [], "defines": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD"], "sourceList": ["../Libraries/CMSIS/CM3/CoreSupport/core_cm3.c", "../Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/arm/startup_stm32f10x_md.s", "../Libraries/STM32F10x_StdPeriph_Driver/src/misc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_adc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_bkp.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_can.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_cec.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_crc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dac.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dbgmcu.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dma.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_exti.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_flash.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_fsmc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_gpio.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_i2c.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_iwdg.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_pwr.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rcc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rtc.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_sdio.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_spi.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_tim.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_usart.c", "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_wwdg.c", "../User/can_app.c", "../User/can_driver.c", "../User/delay.c", "../User/main.c", "../User/stm32f10x_it.c", "../User/system_stm32f10x.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf.exe --bin --output $<EMAIL> $<EMAIL>", "command": "fromelf.exe --bin --output ${KEIL_OUTPUT_DIR}\\${ProjectName}.bin ${KEIL_OUTPUT_DIR}\\${ProjectName}.axf", "disable": false, "abortAfterFailed": true}, {"name": "file_encrypt.exe  $<EMAIL>  $L@L_encrypt.bin  123456789abcdefggdfrthfgdfgefsse", "command": "file_encrypt.exe  ${KEIL_OUTPUT_DIR}\\${ProjectName}.bin  ${KEIL_OUTPUT_DIR}\\@L_encrypt.bin  123456789abcdefggdfrthfgdfgefsse", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m3", "microcontroller-fpu": "cortex-m3", "microcontroller-float": "cortex-m3", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "ro-base": "0x08000000", "rw-base": "0x20000000", "link-scatter": ["\"c:/Users/<USER>/Desktop/基于STM32F103的CAN bootloader程序源码资料/CAN bootloader/app/RVMDK/build/Project/Project.sct\""]}}, "env": {"KEIL_OUTPUT_DIR": "Output", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\基于STM32F103的CAN bootloader程序源码资料\\CAN bootloader\\app\\RVMDK", "workspaceFolderBasename": "RVMDK", "OutDir": "c:\\Users\\<USER>\\Desktop\\基于STM32F103的CAN bootloader程序源码资料\\CAN bootloader\\app\\RVMDK\\build\\Project", "OutDirRoot": "build", "OutDirBase": "build\\Project", "ProjectName": "Project", "ConfigName": "Project", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\基于STM32F103的CAN bootloader程序源码资料\\CAN bootloader\\app\\RVMDK", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\基于STM32F103的CAN bootloader程序源码资料\\CAN bootloader\\app\\RVMDK\\build\\Project\\Project", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.14\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}