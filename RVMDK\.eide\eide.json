{"name": "Project", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Startup", "files": [{"path": "../Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/arm/startup_stm32f10x_hd.s"}, {"path": "../Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/arm/startup_stm32f10x_md.s"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../User/system_stm32f10x.c"}, {"path": "../Libraries/CMSIS/CM3/CoreSupport/core_cm3.c"}], "folders": []}, {"name": "Std<PERSON><PERSON><PERSON>_Driver", "files": [{"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/misc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_adc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_bkp.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_can.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_cec.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_crc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dac.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dbgmcu.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dma.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_exti.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_flash.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_fsmc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_gpio.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_i2c.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_iwdg.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_pwr.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rcc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rtc.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_sdio.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_spi.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_tim.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_usart.c"}, {"path": "../Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_wwdg.c"}], "folders": []}, {"name": "User Source", "files": [{"path": "../User/main.c"}, {"path": "../User/stm32f10x_it.c"}, {"path": "../User/delay.c"}, {"path": "../User/can_driver.c"}, {"path": "../User/can_app.c"}], "folders": []}, {"name": "<PERSON><PERSON>", "files": [{"path": "../User/Readme.txt"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "7f5fcedf28b162853edabb4490abcaaf"}, "targets": {"Project": {"excludeList": ["<virtual_root>/Startup/startup_stm32f10x_hd.s"], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8008000", "size": "0x20000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x", "../Libraries/STM32F10x_StdPeriph_Driver/inc", "../Libraries/CMSIS/CM3/CoreSupport", "../User"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf.exe --bin --output $<EMAIL> $<EMAIL>", "command": "fromelf.exe --bin --output ${KEIL_OUTPUT_DIR}\\${ProjectName}.bin ${KEIL_OUTPUT_DIR}\\${ProjectName}.axf", "disable": false, "abortAfterFailed": true}, {"name": "file_encrypt.exe  $<EMAIL>  $L@L_encrypt.bin  123456789abcdefggdfrthfgdfgefsse", "command": "file_encrypt.exe  ${KEIL_OUTPUT_DIR}\\${ProjectName}.bin  ${KEIL_OUTPUT_DIR}\\@L_encrypt.bin  123456789abcdefggdfrthfgdfgefsse", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}